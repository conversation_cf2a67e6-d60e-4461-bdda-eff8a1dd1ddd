###
LLM翻译辅助库
提供LLM翻译相关的工具函数，包括模板验证、提示词构建、模板选择等功能

Create date:    2025-07-14
Author:         <PERSON><PERSON><PERSON><PERSON><PERSON>
###

debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()

# 语言代码与显示名称的映射
LANGUAGE_MAP = {
  'en': 'English'
  'zh-cn': 'Chinese'
  'zh': 'Traditional Chinese'
  'kr': 'Korean'
}

MODEL_MAPPING = {
  'gpt': 'openAI'
  'openai': 'openAI'
  'claude': 'claude'
  'gemini': 'gemini'
  'deepseek': 'deepseek'
  'grok': 'grok'
  'rm': 'rm',
  'ovh': 'ovh'
}

# 模型优先级映射（数字越小优先级越高）
MODEL_PRIORITY = {
  'rm': 1
  'ovh': 2
  'gemini': 3
  'gpt': 4
  'openai': 5
  'claude': 6
  'deepseek': 7
  'grok': 8
}

module.exports.getModelName = (modelType) ->
  return MODEL_MAPPING[modelType] or modelType

###*
 * 获取模型优先级
 * @param {String} modelType - 模型类型
 * @returns {Number} 优先级数字（越小优先级越高）
###
module.exports.getModelPriority = getModelPriority = (modelType) ->
  priority = if MODEL_PRIORITY[modelType]? then MODEL_PRIORITY[modelType] else 999
  return priority

###*
 * 验证提示词模板结构
 * @param {Object} template - 模板对象
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
###
module.exports.validateTemplateStructure = (template) ->
  errors = []
  
  # 检查必填字段
  requiredFields = ['_id', 'nm', 'ver', 'status', 'scenario', 'm_cfg', 'tpl', 'ts', '_mt']
  for field in requiredFields
    unless template[field]?
      errors.push "缺少必填字段: #{field}"
  
  # 检查模型配置
  if template.m_cfg?
    unless template.m_cfg.m_nm?
      errors.push '缺少模型名称: m_cfg.m_nm'
    
    supportedModels = Object.keys(MODEL_MAPPING)
    if template.m_cfg.m_nm and template.m_cfg.m_nm not in supportedModels
      errors.push "不支持的模型: #{template.m_cfg.m_nm}"
  
  # 检查模板内容
  if template.tpl?
    unless template.tpl.main?
      errors.push '缺少主要模板内容: tpl.main'
  
  # 检查变量定义
  if template.vars? and Array.isArray(template.vars)
    for variable, index in template.vars
      unless variable.nm?
        errors.push "变量 #{index} 缺少名称字段"
      unless variable.tp?
        errors.push "变量 #{index} 缺少类型字段"
      unless variable.req?
        errors.push "变量 #{index} 缺少必填标识"
  
  return {
    valid: errors.length is 0
    errors: errors
  }

###*
 * 获取排序后的提示词模板列表（支持容错机制）
 * @param {String} content - 待翻译内容
 * @param {String} scenario - 翻译场景
 * @param {Array} promptTemplates - 可用的提示词模板列表
 * @returns {Array} 按优先级排序的模板列表
###
module.exports.getSortedPromptTemplates = (content, scenario, promptTemplates) ->
  return [] unless content and promptTemplates and Array.isArray(promptTemplates)

  # 过滤出指定场景的活跃模板
  candidateTemplates = promptTemplates.filter (template) ->
    template.status is 'active' and (template.scenario is scenario)

  return [] if candidateTemplates.length is 0

  # 按模型优先级和版本进行排序
  sortedTemplates = candidateTemplates.sort (a, b) ->
    # 首先按模型优先级排序
    priorityA = getModelPriority(a.m_cfg?.m_nm)
    priorityB = getModelPriority(b.m_cfg?.m_nm)

    if priorityA isnt priorityB
      return priorityA - priorityB

    # 如果优先级相同，按版本排序（版本高的优先）
    versionA = parseFloat(a.ver) or 0
    versionB = parseFloat(b.ver) or 0

    if versionA isnt versionB
      return versionB - versionA

    # 如果版本也相同，按修改时间排序（新的优先）
    timeA = new Date(a._mt).getTime() or 0
    timeB = new Date(b._mt).getTime() or 0

    return timeB - timeA

  return sortedTemplates

###*
 * 替换字符串中的所有变量占位符
 * @param {String} text - 要处理的文本
 * @param {Object} variables - 变量值对象
 * @returns {String} 替换后的文本
###
replaceVariables = (text, variables) ->
  return text unless text

  # 替换已提供的变量
  for varName, varValue of variables
    placeholder = "{#{varName}}"
    text = text.replace(new RegExp(placeholder, 'g'), varValue or '')

  # 替换剩余的占位符为空字符串（处理缺失的可选变量）
  text = text.replace(/\{[^}]+\}/g, '')

  return text

###*
 * 构建完整的翻译提示词
 * @param {Object} template - 提示词模板
 * @param {Object} variables - 变量值对象
 * @returns {Object} 构建结果 {success: boolean, prompt: string, systemPrompt: string, error: string}
###
module.exports.buildTranslationPrompt = buildTranslationPrompt = (template, variables) ->
  return {success: false, error: '模板不能为空'} unless template
  return {success: false, error: '变量不能为空'} unless variables

  # 检查必填变量
  if template.vars? and Array.isArray(template.vars)
    for variable in template.vars
      if variable.req and (not variables[variable.nm]? or variables[variable.nm] is '')
        return {success: false, error: "缺少必填变量: #{variable.nm}"}

  # 替换主要提示词中的变量
  mainPrompt = replaceVariables(template.tpl.main, variables)

  # 替换系统提示词中的变量
  systemPrompt = replaceVariables(template.tpl.sys or '', variables)

  return {
    success: true
    prompt: mainPrompt
    systemPrompt: systemPrompt
    modelConfig: template.m_cfg
  }

###*
 * 获取排序后的过滤模板列表（支持容错机制）
 * @param {String} filterType - 过滤类型
 * @param {Array} promptTemplates - 可用的提示词模板列表
 * @returns {Array} 按优先级排序的过滤模板列表
###
module.exports.getSortedFilterTemplates = (filterType, promptTemplates) ->
  return [] unless filterType and promptTemplates and Array.isArray(promptTemplates)

  # 过滤出指定过滤类型的活跃模板
  candidateTemplates = promptTemplates.filter (template) ->
    template.status is 'active' and template.scenario is filterType

  return [] if candidateTemplates.length is 0

  # 按模型优先级和版本进行排序（复用翻译模板的排序逻辑）
  sortedTemplates = candidateTemplates.sort (a, b) ->
    # 首先按模型优先级排序
    priorityA = getModelPriority(a.m_cfg?.m_nm)
    priorityB = getModelPriority(b.m_cfg?.m_nm)

    if priorityA isnt priorityB
      return priorityA - priorityB

    # 如果优先级相同，按版本排序（版本高的优先）
    versionA = parseFloat(a.ver) or 0
    versionB = parseFloat(b.ver) or 0

    if versionA isnt versionB
      return versionB - versionA

    # 如果版本也相同，按修改时间排序（新的优先）
    timeA = new Date(a._mt).getTime() or 0
    timeB = new Date(b._mt).getTime() or 0

    return timeB - timeA

  return sortedTemplates

###*
 * 构建过滤提示词
 * @param {Object} template - 过滤模板
 * @param {Object} variables - 变量值对象
 * @returns {Object} 构建结果 {success: boolean, prompt: string, systemPrompt: string, error: string}
###
module.exports.buildFilterPrompt = (template, variables) ->
  # 复用翻译提示词构建逻辑
  return buildTranslationPrompt(template, variables)

###*
 * 解析过滤结果
 * @param {String} filterResult - AI模型返回的过滤结果
 * @returns {Object} 解析结果 {passed: boolean, reason: string}
###
module.exports.parseFilterResult = (filterResult) ->
  return {passed: false, reason: '过滤结果为空'} unless filterResult

  originalResult = filterResult.trim()
  lowerResult = originalResult.toLowerCase()

  # 检查是否通过
  if lowerResult.startsWith('pass')
    return {passed: true, reason: '内容通过审核'}

  # 检查是否被拒绝
  if lowerResult.startsWith('reject')
    # 从原始输入提取拒绝原因，保持大小写
    reasonMatch = originalResult.match(/reject:\s*(.+)/i)
    reason = if reasonMatch then reasonMatch[1] else '内容不符合社区规范'
    return {passed: false, reason: reason}

  # 默认情况下拒绝
  return {passed: false, reason: '无法解析过滤结果，默认拒绝'}

###*
 * 验证语言代码
 * @param {String} langCode - 语言代码
 * @returns {Boolean} 是否为有效的语言代码
###
module.exports.isValidLanguageCode = (langCode) ->
  supportedLanguages = Object.keys(LANGUAGE_MAP).concat(Object.values(LANGUAGE_MAP))
  return langCode in supportedLanguages

###*
 * 获取语言显示名称
 * @param {String} langCode - 语言代码
 * @returns {String} 语言显示名称
###
module.exports.getLanguageDisplayName = (langCode) ->
  return LANGUAGE_MAP[langCode] or langCode
