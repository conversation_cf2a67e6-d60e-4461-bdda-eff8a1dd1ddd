debugHelper = require '../debug'
debug = debugHelper.getDebugger()
Translator = require './translator'

class AITranslator extends Translator
  constructor: (apiKey, endpoint, model, prompt, maxUsage) ->
    super(apiKey, endpoint, maxUsage)
    @model = model
    @prompt = prompt

  ###*
   * 使用自定义提示词进行翻译的默认实现
   * 子类应该重写此方法以提供具体实现
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    throw new Error('translateWithCustomPrompt method must be implemented by subclass')

  ###*
   * 通用的invokeFetch结果处理函数
   * 基于最常见的OpenAI格式进行处理，特殊格式的翻译器可以重写此方法
   * @param {Object} result - invokeFetch的返回结果
   * @returns {String} 处理后的翻译内容
  ###
  processInvokeFetchResult: (result) ->
    if result.success
      translatedContent = result.data.choices[0].message.content
      translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
      return translatedContentDeleteN
    else
      throw new Error(result.data.message or result.data)

  ###*
   * 通用的fetch调用方法，统一处理所有基于fetch的API调用
   * @param {Object} data - 要发送的数据
   * @param {Object} options - 可选配置
   * @param {Object} options.headers - 额外的请求头
   * @param {Number} options.timeout - 超时时间（毫秒）
   * @param {String} options.errorPrefix - 错误日志前缀
   * @returns {Promise<Object>} { success: boolean, data: object, response: Response }
  ###
  invokeFetch: (data, options = {}) ->
    defaultOptions = {
      method: 'POST'
      headers: { 'Content-Type': 'application/json' }
      timeout: 30000
      errorPrefix: 'Translation API Error'
    }

    # 合并默认选项和传入选项
    finalOptions = Object.assign({}, defaultOptions, options)

    # 合并headers
    finalHeaders = Object.assign({}, defaultOptions.headers, options.headers or {})

    try
      @use()
      fetchOptions = {
        method: finalOptions.method
        headers: finalHeaders
        body: JSON.stringify(data)
      }

      # 添加超时控制
      if finalOptions.timeout
        fetchOptions.signal = AbortSignal.timeout(finalOptions.timeout)

      debug.debug "Invoking fetch to #{@endpoint} with data:", data
      response = await fetch(@endpoint, fetchOptions)
      ret = await response.json()
      debug.debug 'Fetch response:', { status: response.status, data: ret }

      return {
        success: response.ok,
        data: ret,
        response: response
      }

    catch error
      debug.error "#{finalOptions.errorPrefix}:", {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        endpoint: @endpoint
        data: data
      }
      throw error
    finally
      @release()

module.exports = AITranslator